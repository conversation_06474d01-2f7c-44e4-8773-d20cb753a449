//
//  ProfileHeaderView.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/5/10.
//

import UIKit
import SnapKit
import Kingfisher

class ProfileHeaderView: UIView {
    // MARK: - Properties
    
    // 头部背景图
    private let headerBackgroundView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.backgroundColor = UIColor(hex: "F5F5F5")
        imageView.layer.anchorPoint = CGPoint(x: 0.5, y: 1)
        imageView.isUserInteractionEnabled = false
//        imageView.isHidden = true
        return imageView
    }()
    
    // 头部背景蒙版
    private let headerOverlayView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
//        view.isHidden = true
        view.isUserInteractionEnabled = false
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor.white.withAlphaComponent(0.2).cgColor,
            UIColor(hex: "#F5F5F5").cgColor
        ]
        gradientLayer.locations = [0.0, 1.0]
        gradientLayer.startPoint = CGPoint(x: 0.5, y: 0.0)
        gradientLayer.endPoint = CGPoint(x: 0.5, y: 1.0)
        view.layer.addSublayer(gradientLayer)
        return view
    }()
    
    private let avatarContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        view.clipsToBounds = false
        return view
    }()
    
    private let gradientBorderLayer: CAGradientLayer = {
        let layer = CAGradientLayer()
        layer.colors = [
            UIColor(hex: "#FF5900").cgColor,
            UIColor(hex: "#FF8D36").cgColor
        ]
        layer.startPoint = CGPoint(x: 0, y: 0)
        layer.endPoint = CGPoint(x: 1, y: 1)
        return layer
    }()
    
    private let avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.backgroundColor = UIColor(hex: "#F5F5F5")
        imageView.image = UIImage(named: "default_avatar")
        return imageView
    }()
    
    private let usernameLabel: UILabel = {
        let label = UILabel()
        label.text = "未登录"
        label.textColor = UIColor(hex: "#333333")
        label.font = .boldSystemFont(ofSize: 18)
        return label
    }()
    
    private let uidLabel: UILabel = {
        let label = UILabel()
        label.text = "树小柒号： --"
        label.textColor = UIColor(hex: "#444444")
        label.font = .systemFont(ofSize: 11)
        return label
    }()
    // 我新增：用于显示二维码的按钮
    private lazy var qrCodeButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "td_code_btn"), for: .normal)
        return button
    }()
    
    private let locationLabel: UILabel = {
        let label = UILabel()
        label.text = "IP属地： --"
        label.textColor = UIColor(hex: "#444444")
        label.font = .systemFont(ofSize: 11)
        return label
    }()
    
    private let bioLabel: UILabel = {
        let label = UILabel()
        label.text = "编辑个性签名"
        label.textColor = UIColor(hex: "#777777")
        label.font = .systemFont(ofSize: 13)
        // 修改为0，允许多行显示
        label.numberOfLines = 0
        label.lineBreakMode = .byWordWrapping
        // 设置内容压缩阻力优先级，确保多行文本能正确显示
        label.setContentCompressionResistancePriority(.required, for: .vertical)
        label.setContentHuggingPriority(.required, for: .vertical)
        return label
    }()
    
    private let editButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("编辑资料", for: .normal)
        button.setTitleColor(UIColor(hex: "#444444"), for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 12)
        button.layer.cornerRadius = 5
        button.layer.borderWidth = 0.5
        button.layer.borderColor = UIColor(hex: "#777777").cgColor
        button.backgroundColor = .clear
        return button
    }()
    
    private let membershipCard: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 8
        view.clipsToBounds = true
        let bgLayer = CAGradientLayer()
        bgLayer.colors = [
            UIColor(hex: "#FFF9E3").cgColor,
            UIColor(hex: "#FFF0C9").cgColor
        ]
        bgLayer.locations = [0, 1]
        bgLayer.startPoint = CGPoint(x: 0, y: 0.5)
        bgLayer.endPoint = CGPoint(x: 1, y: 0.5)
        view.layer.addSublayer(bgLayer)
        return view
    }()
    
    private let membershipTitle: UILabel = {
        let label = UILabel()
        label.text = "F1普通会员"
        label.textColor = UIColor(hex: "#8D4600")
        label.font = .boldSystemFont(ofSize: 16)
        return label
    }()
    
    private let membershipExpiry: UILabel = {
        let label = UILabel()
        label.text = "有效期至2025-2-25"
        label.textColor = UIColor(hex: "#C56F1A")
        label.font = .systemFont(ofSize: 12)
        return label
    }()
    
    private let benefitsLabel: UILabel = {
        let label = UILabel()
        label.text = "尊享12项专属权益"
        label.textColor = UIColor(hex: "#8D4600")
        label.font = .systemFont(ofSize: 12)
        return label
    }()
    
    private let benefitsArrow: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "vip_arrow_right")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private let followingCountLabel = UILabel()
    private let followersCountLabel = UILabel()
    private let likesCountLabel = UILabel()
    
    // 回调
    var onFollowingTapped: (() -> Void)?
    var onFollowersTapped: (() -> Void)?
    var onAvatarTapped: (() -> Void)?
    var onEditTapped: (() -> Void)?
    var onLikesTapped: (() -> Void)?
    // 我新增：扫码按钮点击回调
    var onQRCodeTapped: (() -> Void)?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func createStatsView(title: String, countLabel: UILabel) -> UIView {
        let container = UIView()
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 4
        stackView.alignment = .center
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.textColor = UIColor(hex: "#444444")
        titleLabel.font = .boldSystemFont(ofSize: 13)
        countLabel.text = "0"
        countLabel.textColor = UIColor(hex: "#444444")
        countLabel.font = .boldSystemFont(ofSize: 13)
        stackView.addArrangedSubview(titleLabel)
        stackView.addArrangedSubview(countLabel)
        container.addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(14)
        }
        container.isUserInteractionEnabled = true
        return container
    }
    
    private func setupStatsAndMembership() {
        let followingStats = createStatsView(title: "关注", countLabel: followingCountLabel)
        let followersStats = createStatsView(title: "粉丝", countLabel: followersCountLabel)
        let likesStats = createStatsView(title: "获赞", countLabel: likesCountLabel)
        addSubview(followingStats)
        addSubview(followersStats)
        addSubview(likesStats)
        followingStats.snp.makeConstraints { make in
            make.left.equalTo(20)
            make.top.equalTo(bioLabel.snp.bottom).offset(8)
        }
        followersStats.snp.makeConstraints { make in
            make.left.equalTo(followingStats.snp.right).offset(20)
            make.centerY.equalTo(followingStats)
        }
        likesStats.snp.makeConstraints { make in
            make.left.equalTo(followersStats.snp.right).offset(20)
            make.centerY.equalTo(followingStats)
        }
        let followingTapGesture = UITapGestureRecognizer(target: self, action: #selector(followingTapped))
        followingStats.addGestureRecognizer(followingTapGesture)
        let followersTapGesture = UITapGestureRecognizer(target: self, action: #selector(followersTapped))
        followersStats.addGestureRecognizer(followersTapGesture)
        let likesTapGesture = UITapGestureRecognizer(target: self, action: #selector(likesTapped))
        likesStats.addGestureRecognizer(likesTapGesture)

        // 移除会员卡相关 UI，不再添加 membershipCard 及其子视图
        // 调整底部间距
        likesStats.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    @objc private func avatarTapped() {
        print("头像被点击")
        onAvatarTapped?()
    }
    @objc private func editButtonTapped() {
        print("编辑按钮被点击")
        onEditTapped?()
    }
    @objc private func followingTapped() {
        print("关注被点击")
        onFollowingTapped?()
    }
    @objc private func followersTapped() {
        print("粉丝被点击")
        onFollowersTapped?()
    }
    @objc private func likesTapped() {
        print("获赞被点击")
        onLikesTapped?()
    }
    // 我新增：二维码按钮点击方法
    @objc private func qrCodeButtonTapped() {
        onQRCodeTapped?()
    }
    
    private func setupUI() {
        backgroundColor = UIColor(hex: "#F5F5F5")
        // 先添加背景图和蒙版，确保在最底层且不拦截事件
        addSubview(headerBackgroundView)
        headerBackgroundView.isUserInteractionEnabled = false
        headerBackgroundView.addSubview(headerOverlayView)
        headerOverlayView.isUserInteractionEnabled = false
        // 高度 201 pt 已减去会员区的 64 pt。
        // 新增 `offset(60)` 将背景图整体下移，使头像位于背景中心位置，避免只显示下半部分。
        // 可按视觉效果微调 `60`，例如 `50`/`70`。
        // 这样头像背景图将更居中展示，视觉恢复平衡。如需再次微调告诉我具体数值即可！
        let initialHeight: CGFloat = 201
        headerBackgroundView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(initialHeight)
            make.top.equalTo(self.snp.top).offset(30)
        }
        headerOverlayView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        // 再添加所有交互控件，确保在上层
        addSubview(avatarContainer)
        avatarContainer.layer.cornerRadius = 33
        avatarContainer.snp.makeConstraints { make in
            make.top.equalTo(24)
            make.left.equalTo(20)
            make.size.equalTo(66)
        }
        gradientBorderLayer.frame = CGRect(x: -1, y: -1, width: 66 + 2, height: 66 + 2)
        gradientBorderLayer.cornerRadius = 34
        avatarContainer.layer.addSublayer(gradientBorderLayer)
        avatarContainer.addSubview(avatarImageView)
        avatarImageView.layer.cornerRadius = 32
        avatarImageView.isUserInteractionEnabled = true
        avatarImageView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(64)
        }
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(avatarTapped))
        avatarContainer.isUserInteractionEnabled = true
        avatarContainer.addGestureRecognizer(tapGesture)
        addSubview(usernameLabel)
        usernameLabel.snp.makeConstraints { make in
            make.top.equalTo(avatarContainer.snp.top).offset(6)
            make.left.equalTo(avatarContainer.snp.right).offset(8)
            make.height.equalTo(17)
        }
        addSubview(uidLabel)
        uidLabel.snp.makeConstraints { make in
            make.top.equalTo(usernameLabel.snp.bottom).offset(8)
            make.left.equalTo(usernameLabel.snp.left)
            make.height.equalTo(11)
        }
        // 我新增：添加二维码按钮
        addSubview(qrCodeButton)
        qrCodeButton.snp.makeConstraints { make in
            make.left.equalTo(uidLabel.snp.right).offset(4)
            make.centerY.equalTo(uidLabel)
            make.size.equalTo(14)
        }
        qrCodeButton.addTarget(self, action: #selector(qrCodeButtonTapped), for: .touchUpInside)
        addSubview(locationLabel)
        locationLabel.snp.makeConstraints { make in
            make.top.equalTo(uidLabel.snp.bottom).offset(5)
            make.left.equalTo(uidLabel.snp.left)
            make.height.equalTo(11)
        }
        addSubview(bioLabel)
        bioLabel.snp.makeConstraints { make in
            make.top.equalTo(avatarContainer.snp.bottom).offset(14)
            make.left.equalTo(20)
            make.right.equalTo(-20) // 添加右边距，与左边距保持一致
            // 移除固定高度，让标签根据内容自适应高度
        }
        addSubview(editButton)
        editButton.snp.remakeConstraints { make in
            make.size.equalTo(CGSize(width: 72, height: 22))
            make.top.equalTo(bioLabel.snp.bottom).offset(8) // 与统计信息保持同一水平线
            make.right.equalToSuperview().offset(-21)
        }
        editButton.addTarget(self, action: #selector(editButtonTapped), for: .touchUpInside)
        setupStatsAndMembership()
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        CATransaction.begin()
        CATransaction.setDisableActions(true)
        if let borderLayer = membershipCard.layer.sublayers?.first as? CAGradientLayer {
            borderLayer.frame = membershipCard.bounds
        }
        if let bgLayer = membershipCard.layer.sublayers?.last as? CAGradientLayer {
            bgLayer.frame = membershipCard.bounds.insetBy(dx: 1, dy: 1)
        }
        if let gradientLayer = headerOverlayView.layer.sublayers?.first as? CAGradientLayer {
            if gradientLayer.frame != headerOverlayView.bounds {
                gradientLayer.frame = headerOverlayView.bounds
            }
        }
        CATransaction.commit()
    }
    
    // 下拉放大动效
    func updateHeaderBackgroundFrame(offsetY: CGFloat, safeAreaTop: CGFloat, originalHeight: CGFloat) {
        let heightToUse = originalHeight
        let widthToUse = bounds.width
        guard widthToUse > 0, heightToUse > 0 else { return }
        let gradientLayer = headerOverlayView.layer.sublayers?.first as? CAGradientLayer
        CATransaction.begin()
        CATransaction.setDisableActions(true)
        if offsetY < 0 {
            // 修正：加上safeAreaTop，保证顶部不漏
            // 增加放大倍数（1.2 倍），避免快速出现顶部留白
            let scale = (heightToUse + abs(offsetY) * 1.2 + safeAreaTop) / heightToUse
            headerBackgroundView.transform = CGAffineTransform(scaleX: scale, y: scale)
            gradientLayer?.frame = headerOverlayView.bounds
        } else {
            if headerBackgroundView.transform != .identity {
                headerBackgroundView.transform = .identity
            }
            gradientLayer?.frame = headerOverlayView.bounds
        }
        CATransaction.commit()
    }
    
    // 配置数据
    public func configure(with userInfo: UserInfoData?) {
        if let user = userInfo {
            let placeholder = UIImage(named: "default_avatar")

            // 设置头像
            if !user.wxAvator.isEmpty, let url = URL(string: user.wxAvator) {
                avatarImageView.kf.setImage(with: url, placeholder: placeholder)
            } else {
                avatarImageView.image = placeholder
            }

            // 设置背景图：优先使用背景图，没有背景图时使用头像图
            if let backgroundImage = user.backgroundImage, !backgroundImage.isEmpty, let backgroundURL = URL(string: backgroundImage) {
                // 有背景图，使用背景图
                headerBackgroundView.kf.setImage(with: backgroundURL, placeholder: nil)
            } else if !user.wxAvator.isEmpty, let avatarURL = URL(string: user.wxAvator) {
                // 没有背景图但有头像，使用头像作为背景
                headerBackgroundView.kf.setImage(with: avatarURL, placeholder: nil)
            } else {
                // 既没有背景图也没有头像，清空背景
                headerBackgroundView.image = nil
            }
            usernameLabel.text = user.displayNickName
            uidLabel.text = "树小柒号：\(user.customerAccount.isEmpty ? "--" : user.customerAccount)"

            // 获取IP属地信息
            updateLocationInfo()

            // 从新旧结构中解析个性签名
            let rawSnapshot = user.personalitySign?.snapshot ?? ""
            let parsedSignature = parseSignature(from: rawSnapshot, mapping: user.personalitySign?.mentionedUser)
            if parsedSignature.isEmpty {
                bioLabel.text = "编辑个性签名"
            } else {
                bioLabel.attributedText = attributedSignature(from: parsedSignature, mentionedUser: user.personalitySign?.mentionedUser)
            }
            followingCountLabel.text = "\(user.followNumber)"
            followersCountLabel.text = "\(user.fansNumber)"
            likesCountLabel.text = "\(user.likeNumber)"
        } else {
            avatarImageView.image = UIImage(named: "default_avatar")
            usernameLabel.text = "点击登录"
            uidLabel.text = "树小柒号：--"
            bioLabel.text = "登录后查看更多信息"
            followingCountLabel.text = "0"
            followersCountLabel.text = "0"
            likesCountLabel.text = "0"
            // 移除会员卡相关 UI，不再添加 membershipCard 及其子视图
            // 调整底部间距
            likesCountLabel.snp.remakeConstraints { make in
                make.bottom.equalToSuperview().offset(-16)
            }
        }
        setNeedsLayout()
        layoutIfNeeded()
    }
    
    // 新增：暴露一个fittingHeight(for:)方法用于外部计算高度
    public func fittingHeight(for width: CGFloat) -> CGFloat {
        // 设置临时frame以确保约束能正确计算
        let tempFrame = CGRect(x: 0, y: 0, width: width, height: 0)
        self.frame = tempFrame

        // 强制布局更新，确保多行标签能正确计算高度
        setNeedsLayout()
        layoutIfNeeded()

        let targetSize = CGSize(width: width, height: UIView.layoutFittingCompressedSize.height)
        let calculatedHeight = systemLayoutSizeFitting(targetSize,
                                       withHorizontalFittingPriority: .required,
                                       verticalFittingPriority: .fittingSizeLevel).height

        // 确保最小高度，避免布局异常
        return max(calculatedHeight, 200)
    }
    
    // 公开头像图片，便于外部获取
    public var avatarImage: UIImage? {
        return avatarImageView.image
    }

    // MARK: - Signature Helper

    /// 将个性签名中的“@用户名”部分高亮显示
    /// - Parameters:
    ///   - signature: 已经解析过的个性签名（ID已替换为昵称）
    ///   - mentionedUser: 用户ID到昵称的映射，用于确定高亮范围
    /// - Returns: 处理后的富文本
    private func attributedSignature(from signature: String, mentionedUser: [String: String]? = nil) -> NSAttributedString {
        let attributed = NSMutableAttributedString(string: signature)
        // 默认颜色
        let fullRange = NSRange(location: 0, length: attributed.length)
        attributed.addAttribute(.foregroundColor, value: UIColor(hex: "#777777"), range: fullRange)

        // 如果有mentionedUser映射，使用精确匹配
        if let mentionedUser = mentionedUser, !mentionedUser.isEmpty {
            for (_, nickname) in mentionedUser {
                let searchText = "@\(nickname)"
                let nsString = signature as NSString
                var searchRange = NSRange(location: 0, length: nsString.length)

                while searchRange.location < nsString.length {
                    let foundRange = nsString.range(of: searchText, options: [], range: searchRange)
                    if foundRange.location == NSNotFound {
                        break
                    }

                    // 高亮找到的@用户名
                    attributed.addAttribute(.foregroundColor, value: UIColor(hex: "#4478F5"), range: foundRange)

                    // 继续搜索下一个匹配
                    searchRange.location = foundRange.location + foundRange.length
                    searchRange.length = nsString.length - searchRange.location
                }
            }
        } else {
            // 如果没有mentionedUser映射，使用通用解析
            let nsString = signature as NSString
            var searchRange = NSRange(location: 0, length: nsString.length)

            while searchRange.location < nsString.length {
                let atRange = nsString.range(of: "@", options: [], range: searchRange)
                if atRange.location == NSNotFound {
                    break
                }

                // 找到@符号，开始解析用户名
                var usernameEnd = atRange.location + atRange.length
                let maxEnd = min(nsString.length, usernameEnd + 50) // 限制用户名最大长度

                // 查找用户名的结束位置
                while usernameEnd < maxEnd {
                    let char = nsString.character(at: usernameEnd)
                    let isValidChar = (char >= 48 && char <= 57) ||  // 0-9
                                     (char >= 65 && char <= 90) ||   // A-Z
                                     (char >= 97 && char <= 122) ||  // a-z
                                     char == 95 ||                   // _
                                     char == 45 ||                   // -
                                     (char >= 0x4e00 && char <= 0x9fa5) // 中文字符

                    if !isValidChar {
                        break
                    }
                    usernameEnd += 1
                }

                // 如果找到了有效的用户名（长度大于1，即除了@符号还有其他字符）
                if usernameEnd > atRange.location + 1 {
                    let mentionRange = NSRange(location: atRange.location, length: usernameEnd - atRange.location)
                    attributed.addAttribute(.foregroundColor, value: UIColor(hex: "#4478F5"), range: mentionRange)
                }

                // 继续搜索下一个@符号
                searchRange.location = usernameEnd
                searchRange.length = nsString.length - searchRange.location
            }
        }

        return attributed
    }

    /// 解析 snapshot，将 @id 替换为用户昵称，并去除多余的反斜杠
    private func parseSignature(from snapshot: String, mapping: [String: String]?) -> String {
        var result = snapshot
        if result.isEmpty { return "" }
        // 去除转义反斜杠
        result = result.replacingOccurrences(of: "\\", with: "")
        if let map = mapping {
            for (id, name) in map {
                result = result.replacingOccurrences(of: "@\(id)", with: "@\(name)")
            }
        }
        return result
    }

    /// 更新IP属地信息
    private func updateLocationInfo() {
        // 使用缓存机制：先显示缓存，再更新网络结果
        NetworkUtils.getIPLocationWithCache(
            cacheCompletion: { [weak self] cachedProvince in
                guard let self = self else { return }

                if let province = cachedProvince, !province.isEmpty {
                    // 有缓存，立即显示
                    self.locationLabel.text = "IP属地： \(province)"
                } else {
                    // 没有缓存，显示获取中状态
                    self.locationLabel.text = "IP属地： 获取中..."
                }
            },
            networkCompletion: { [weak self] networkProvince in
                guard let self = self else { return }

                if let province = networkProvince, !province.isEmpty {
                    // 网络查询成功，更新显示
                    self.locationLabel.text = "IP属地： \(province)"
                } else {
                    // 网络查询失败，使用GPS定位获取省级信息
                    self.getGPSLocationProvince()
                }
            }
        )
    }

    /// 使用GPS定位获取省级信息作为备选方案
    private func getGPSLocationProvince() {
        LocationManager.shared.getCurrentAreaCode { [weak self] areaCode, address in
            guard let self = self else { return }

            if let address = address, !address.isEmpty {
                // 从地址中提取省级信息
                let components = address.components(separatedBy: " ")
                let province = components.first ?? ""
                if !province.isEmpty {
                    self.locationLabel.text = "IP属地： \(province)"
                } else {
                    self.locationLabel.text = "IP属地： --"
                }
            } else {
                self.locationLabel.text = "IP属地： --"
            }
        }
    }
}
