
import SmartCodable

/// 短视频作品上传响应
struct ShortVideoWorksAddResponse: SmartCodable {
    var status: Int = 0
    var errMsg: String?
    var msg: String?
    var data: ShortVideoWorksAddData?

    /// 是否成功
    var isSuccess: Bool {
        return status == 200
    }

    /// 获取显示给用户的消息
    var displayMessage: String {
        if let message = msg, !message.isEmpty {
            return message
        }
        if let errorMsg = errMsg, !errorMsg.isEmpty {
            return errorMsg
        }
        return isSuccess ? "成功" : "未知错误"
    }
}

/// 短视频作品上传响应数据
struct ShortVideoWorksAddData: SmartCodable {
    /// 作品ID
    var worksId: Int?
    /// 其他返回数据字段可根据实际API响应添加
}

/// 短视频作品上传请求（用于构建请求参数）
struct ShortVideoWorksAddRequest: SmartCodable {
    /// 位置地址
    var address: String?
    /// 是否允许评论 0 不允许 1 允许
    var allowComment: Int = 1
    /// 城市行政区划代码
    var areaCode: String?
    /// 视频时长,单位秒
    var duration: Int?
    /// 是否定时发布， 是则该字段记录 未来时间
    var extValue: String?
    /// 是否仅限关注的人评价 0 都可以评价 1 只有关注的才可以评价
    var followComment: Int = 0
    /// 商品橱窗商品id，json数组
    var goodIds: [String]?
    /// 纬度
    var lat: String?
    /// 经度
    var lng: String?
    /// 私密性:1-公开 2-关注我的 3-我关注的 json数组
    var privacy: [Int] = [1]
    /// 视频大小,单位B
    var size: Int?
    /// 云点播视频文件id
    var videoId: String = ""
    /// 作品分类id
    var worksCategoryId: Int = 1
    /// 作品封面图片
    var worksCoverImg: String = ""
    /// 作品描述
    var worksDescribe: String?
    /// 作品标题
    var worksTitle: String = "测试视频"
    /// 作品类型 1-视频 2-笔记
    var worksType: Int = 1
    /// 作品url,json数组
    var worksUrl: [String]?
    /// 编辑配置id，用于埋点统计，json对象
    /// 格式：["类型": "配置id"] 类型：1-特效 2-美颜 3-音乐 4-滤镜
    var useEditConfigId: [String: String]?

    // 创建一个默认模型的便利方法
    static func createDefault(videoId: String, coverImg: String) -> ShortVideoWorksAddRequest {
        var request = ShortVideoWorksAddRequest()
        request.videoId = videoId
        request.worksCoverImg = coverImg
        request.worksTitle = "测试视频_\(Int(Date().timeIntervalSince1970))"
        return request
    }
}
